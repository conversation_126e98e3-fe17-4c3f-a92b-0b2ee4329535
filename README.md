# 优惠券自动领取脚本

基于H5网页JavaScript代码分析实现的自动化优惠券领取工具。

## 功能特点

- ✅ **精确的UUID生成**：完全复制JavaScript中的platSsn生成算法
- ✅ **并发领取**：同时为三种优惠券类型发送请求
- ✅ **智能重试**：基于响应内容判断是否需要重试
- ✅ **实时日志**：彩色输出，清晰显示每个请求的详细信息
- ✅ **异常处理**：完善的网络异常和超时处理
- ✅ **统计报告**：显示每个类型的尝试次数和最终结果

## 支持的优惠券类型

- `WATER_PURIFIER` - 净水器
- `WASHER` - 洗衣机  
- `FRIDGE` - 冰箱

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. **获取userInfo**：
   - 打开浏览器开发者工具
   - 在H5页面手动领取一次优惠券
   - 在Network标签中找到POST请求
   - 复制请求体中的`userInfo`字段值

2. **配置脚本**：
   ```python
   # 在coupon_grabber.py的main()函数中修改这一行：
   USER_INFO = "你的加密用户信息字符串"
   ```

3. **运行脚本**：
   ```bash
   python coupon_grabber.py
   ```

## 核心算法说明

### platSsn生成算法

基于JavaScript代码分析，platSsn是一个32位十六进制UUID v4格式字符串：

```javascript
// 原始JavaScript代码
function i() {
    const t = [], e = "0123456789abcdef";
    for (let i = 0; i < 36; i++)
        t[i] = e.substr(Math.floor(16 * Math.random()), 1);
    t[14] = "4",
    t[19] = e.substr(3 & t[19] | 8, 1),
    t[8] = t[13] = t[18] = t[23] = "";
    const s = t.join("");
    return s
}
```

Python实现完全复制了这个算法的逻辑。

### 重试逻辑

脚本会分析响应内容：
- **成功关键词**：`["领取成功", "成功领取", "抢券成功", "领券成功"]`
- **重试关键词**：`["活动火爆", "领取失败", "资格已用完", "请稍后再试", "系统繁忙"]`

只有检测到成功关键词才会停止重试。

## 日志输出示例

```
[14:30:15.123] [净水器] [INFO] 第1次尝试 | platSsn: 93a2a535...dae68aa0
[14:30:15.456] [净水器] [INFO] 响应状态码: 200
[14:30:15.457] [净水器] [INFO] 响应内容: {"code":500,"msg":"活动火爆，请稍后再试"}
[14:30:16.789] [净水器] [SUCCESS] 🎉 优惠券领取成功！
```

## 注意事项

- 请合理使用，避免对服务器造成过大压力
- 建议在网络状况良好时使用
- 如遇到IP限制，可适当调整请求间隔
- 请确保userInfo的有效性

## 技术细节

- **异步并发**：使用`asyncio`和`aiohttp`实现高性能并发请求
- **错误处理**：完善的异常捕获和超时处理机制
- **彩色输出**：使用`colorama`库实现跨平台彩色终端输出
- **随机延迟**：避免请求过于频繁被服务器限制

## 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。
