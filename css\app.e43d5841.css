#app {
    font-family: Avenir,Helvetica,Arial,sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50
}

* {
    padding: 0;
    margin: 0
}

.homeUni .time {
    padding-top: 30rem
}

.channel-cel-new[data-v-77d5a00d] {
    margin-top: 15.625rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel[data-v-77d5a00d] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    padding: 2.5rem 0;
    text-align: center;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel[data-v-77d5a00d] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel .bank-event-flex[data-v-77d5a00d] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img[data-v-77d5a00d] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p[data-v-77d5a00d] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel[data-v-77d5a00d] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex[data-v-77d5a00d] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img[data-v-77d5a00d] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel[data-v-77d5a00d] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p[data-v-77d5a00d] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span[data-v-77d5a00d] {
    color: #d3443e
}

.home[data-v-77d5a00d] {
    background: #35714e url(../img/bkg3.7154d0e6.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.home .bank-event-flex[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .bank-event-flex img[data-v-77d5a00d] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home .time[data-v-77d5a00d] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event[data-v-77d5a00d] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home .event .event-bkg-wrapper[data-v-77d5a00d] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg[data-v-77d5a00d] {
    width: 100%;
    height: auto
}

.home .event .event-desc[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home .event .bank-1[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1[data-v-77d5a00d]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance[data-v-77d5a00d] {
    margin-top: .625rem
}

.home .event .bank-event[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img[data-v-77d5a00d] {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled[data-v-77d5a00d] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img[data-v-77d5a00d]:first-child {
    padding-right: 2%
}

.home .event .bank-3[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span[data-v-77d5a00d] {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2[data-v-77d5a00d],.home .event .bkg-3[data-v-77d5a00d] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule[data-v-77d5a00d] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img[data-v-77d5a00d] {
    width: 100%
}

.home .rule .bkg-1[data-v-77d5a00d] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2[data-v-77d5a00d] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3[data-v-77d5a00d] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1[data-v-77d5a00d]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2[data-v-77d5a00d]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3[data-v-77d5a00d] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3[data-v-77d5a00d]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title[data-v-77d5a00d] {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong[data-v-77d5a00d] {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip[data-v-77d5a00d] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.homeViewWechat {
    background: #7ebd8a!important;
    min-height: 50rem;
    width: 100%;
    height: 100%
}

.homeViewWechat .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.homeViewWechat .time {
    padding-top: 12.5rem!important;
    margin: 0 auto
}

.homeViewWechat .event,.homeViewWechat .time {
    position: relative;
    width: 90%;
    height: auto;
    z-index: 2
}

.homeViewWechat .event {
    left: 5%;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem
}

.homeViewWechat .event .event-bkg-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.homeViewWechat .event .event-bkg-wrapper .event-bkg {
    width: 100%;
    height: auto
}

.homeViewWechat .event .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.homeViewWechat .event .event-desc .strong {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechat .event .event-yun {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.homeViewWechat .event .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.homeViewWechat .event .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.homeViewWechat .event .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.homeViewWechat .event .bank-chance {
    margin-top: .625rem
}

.homeViewWechat .event .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeViewWechat .event .bank-event img {
    display: inline-block;
    width: 48%
}

.homeViewWechat .event .bank-event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.homeViewWechat .event .bank-event img:first-child {
    padding-right: 2%
}

.homeViewWechat .event .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.homeViewWechat .event .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechat .event .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.homeViewWechat .event .bkg-2,.homeViewWechat .event .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.homeViewWechat .rule {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.homeViewWechat .rule img {
    width: 100%
}

.homeViewWechat .rule .bkg-1 {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.homeViewWechat .rule .bkg-2 {
    position: absolute;
    z-index: 2;
    left: 0
}

.homeViewWechat .rule .bkg-3 {
    position: absolute;
    z-index: 3;
    left: 0
}

.homeViewWechat .rule .rule-1 {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem!important;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-1:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-2 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-2:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-3 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-3:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-4 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-4:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-5 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-5:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-6 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-6:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-7 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-7:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-8 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-8:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .rule-9 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechat .rule .rule-9:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechat .rule .title {
    color: #35714e;
    font-weight: 700
}

.homeViewWechat .rule .strong {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechat .rule .tip {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.channel-cel-new[data-v-d6a7e404] {
    margin-top: 12.5rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel[data-v-d6a7e404] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    padding: 1.25rem .625rem;
    text-align: left;
    color: #2c3e50;
    background: #fffbeb;
    border-radius: .625rem;
    font-size: 1rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-weight: 400
}

.channel-cel-new .bank-chance-cel .cel-title[data-v-d6a7e404] {
    font-size: 1.125rem
}

.channel-cel-new .bank-chance-cel .strong[data-v-d6a7e404] {
    color: #d3443e;
    font-weight: 700
}

.channel-cel-new .unionpay-cel[data-v-d6a7e404] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel .bank-event-flex[data-v-d6a7e404] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img[data-v-d6a7e404] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p[data-v-d6a7e404] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel[data-v-d6a7e404] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex[data-v-d6a7e404] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img[data-v-d6a7e404] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel[data-v-d6a7e404] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p[data-v-d6a7e404] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span[data-v-d6a7e404] {
    color: #d3443e
}

.home[data-v-d6a7e404] {
    background: #35714e url(../img/bkg.0de406b6.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.home .bank-event-flex[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .bank-event-flex img[data-v-d6a7e404] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home .time[data-v-d6a7e404] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event[data-v-d6a7e404] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home .event .event-bkg-wrapper[data-v-d6a7e404] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg[data-v-d6a7e404] {
    width: 100%;
    height: auto
}

.home .event .event-desc[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home .event .bank-1[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1[data-v-d6a7e404]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance[data-v-d6a7e404] {
    margin-top: .625rem
}

.home .event .bank-event[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img[data-v-d6a7e404] {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled[data-v-d6a7e404] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img[data-v-d6a7e404]:first-child {
    padding-right: 2%
}

.home .event .bank-3[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span[data-v-d6a7e404] {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2[data-v-d6a7e404],.home .event .bkg-3[data-v-d6a7e404] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule[data-v-d6a7e404] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img[data-v-d6a7e404] {
    width: 100%
}

.home .rule .bkg-1[data-v-d6a7e404] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2[data-v-d6a7e404] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3[data-v-d6a7e404] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1[data-v-d6a7e404]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2[data-v-d6a7e404]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3[data-v-d6a7e404] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3[data-v-d6a7e404]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title[data-v-d6a7e404] {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong[data-v-d6a7e404] {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip[data-v-d6a7e404] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.homeUni-202503 {
    background: #3069da;
    min-height: 50rem;
    width: 100%
}

.homeUni-202503 .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.homeUni-202503 .time2 {
    padding-top: 36.25rem!important;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 5
}

.homeUni-202503 .time2 .time-1 {
    color: #dd0500;
    font-size: 1.4375rem;
    position: relative;
    z-index: 10;
    font-weight: 700;
    padding-top: 6.25rem
}

.homeUni-202503 .time2 .time-2 {
    font-size: 1.25rem;
    color: #666;
    position: relative;
    z-index: 10;
    font-weight: 700;
    padding-top: .75rem;
    padding-bottom: 2.1875rem
}

.homeUni-202503 .time2 .event-bkg {
    width: 100%;
    height: auto;
    position: absolute;
    top: 36.25rem;
    left: 0
}

.homeUni-202503 .time2 .bkg-3 {
    position: absolute;
    width: 100%;
    height: 15%;
    z-index: 2;
    bottom: 0;
    left: 0
}

.homeUni-202503 .event2 {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: 2.5rem;
    z-index: 2;
    font-size: 1.25rem;
    color: #000
}

.homeUni-202503 .event2 .strong {
    color: #dd0500;
    font-weight: 700
}

.homeUni-202503 .event2 .event-bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto
}

.homeUni-202503 .event2 .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.homeUni-202503 .event2 .event-yun {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.homeUni-202503 .event2 .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.homeUni-202503 .event2 .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.homeUni-202503 .event2 .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.homeUni-202503 .event2 .bank-chance {
    margin-top: .625rem
}

.homeUni-202503 .event2 .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeUni-202503 .event2 .bank-event img {
    display: inline-block;
    width: 48%
}

.homeUni-202503 .event2 .bank-event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.homeUni-202503 .event2 .bank-event img:first-child {
    padding-right: 2%
}

.homeUni-202503 .event2 .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.homeUni-202503 .event2 .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.homeUni-202503 .event2 .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.homeUni-202503 .event2 .bkg-2 {
    top: 5.625rem;
    height: 55%
}

.homeUni-202503 .event2 .bkg-2,.homeUni-202503 .event2 .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.homeUni-202503 .button {
    font-size: 1.625rem;
    color: #fff;
    font-weight: 700;
    height: 4.375rem;
    line-height: 4.375rem;
    width: 85%;
    text-align: center;
    margin: 1.875rem auto;
    background: url(../img/button.bf2335c4.png);
    background-size: 100% 100%
}

.homeUni-202503 .button.disabled {
    background: #c6c6c6;
    border-radius: 3.125rem
}

.homeUni-202503 .tip {
    padding: 0 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    font-size: 1rem;
    color: #fff;
    text-align: left;
    background: #3069da
}

.homeUni-202503 .tip .strong {
    color: #ffdc00;
    font-weight: 700
}

.bcViewWechat {
    background: #7ebd8a;
    min-height: 50rem;
    width: 100%;
    height: 100%
}

.bcViewWechat .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.bcViewWechat .time {
    padding-top: 12.5rem!important;
    margin: 0 auto
}

.bcViewWechat .event,.bcViewWechat .time {
    position: relative;
    width: 90%;
    height: auto;
    z-index: 2
}

.bcViewWechat .event {
    left: 5%;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem
}

.bcViewWechat .event .event-bkg-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.bcViewWechat .event .event-bkg-wrapper .event-bkg {
    width: 100%;
    height: auto
}

.bcViewWechat .event .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.bcViewWechat .event .event-desc .strong {
    color: #d3443e;
    font-weight: 700
}

.bcViewWechat .event .event-yun {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.bcViewWechat .event .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.bcViewWechat .event .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.bcViewWechat .event .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.bcViewWechat .event .bank-chance {
    margin-top: .625rem
}

.bcViewWechat .event .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.bcViewWechat .event .bank-event img {
    display: inline-block;
    width: 48%
}

.bcViewWechat .event .bank-event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.bcViewWechat .event .bank-event img:first-child {
    padding-right: 2%
}

.bcViewWechat .event .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.bcViewWechat .event .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.bcViewWechat .event .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.bcViewWechat .event .bkg-2,.bcViewWechat .event .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.bcViewWechat .rule {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.bcViewWechat .rule img {
    width: 100%
}

.bcViewWechat .rule .bkg-1 {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.bcViewWechat .rule .bkg-2 {
    position: absolute;
    z-index: 2;
    left: 0
}

.bcViewWechat .rule .bkg-3 {
    position: absolute;
    z-index: 3;
    left: 0
}

.bcViewWechat .rule .rule-1 {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-1:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-2 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-2:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-3 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-3:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-4 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-4:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-5 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-5:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-6 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-6:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-7 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-7:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-8 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-8:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .rule-9 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.bcViewWechat .rule .rule-9:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.bcViewWechat .rule .title {
    color: #35714e;
    font-weight: 700
}

.bcViewWechat .rule .strong {
    color: #d3443e;
    font-weight: 700
}

.bcViewWechat .rule .tip {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.disabled[data-v-0db02a26] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.channel-cel-new[data-v-0db02a26] {
    margin-top: 12.5rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel[data-v-0db02a26] {
    text-align: center;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.channel-cel-new .bank-chance-cel[data-v-0db02a26],.channel-cel-new .unionpay-cel[data-v-0db02a26] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel[data-v-0db02a26] {
    margin-top: 1.25rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .unionpay-cel .bank-event-flex[data-v-0db02a26] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img[data-v-0db02a26] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p[data-v-0db02a26] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel[data-v-0db02a26] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex[data-v-0db02a26] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img[data-v-0db02a26] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel[data-v-0db02a26] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p[data-v-0db02a26] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span[data-v-0db02a26] {
    color: #d3443e
}

.home[data-v-0db02a26] {
    background: #35714e url(../img/bc-bgk.ddc80947.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.home .bank-event-flex[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .bank-event-flex img[data-v-0db02a26] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home .time[data-v-0db02a26] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event[data-v-0db02a26] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home .event .event-bkg-wrapper[data-v-0db02a26] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg[data-v-0db02a26] {
    width: 100%;
    height: auto
}

.home .event .event-desc[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home .event .bank-1[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1[data-v-0db02a26]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance[data-v-0db02a26] {
    margin-top: .625rem
}

.home .event .bank-event[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img[data-v-0db02a26] {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled[data-v-0db02a26] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img[data-v-0db02a26]:first-child {
    padding-right: 2%
}

.home .event .bank-3[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span[data-v-0db02a26] {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2[data-v-0db02a26],.home .event .bkg-3[data-v-0db02a26] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule[data-v-0db02a26] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img[data-v-0db02a26] {
    width: 100%
}

.home .rule .bkg-1[data-v-0db02a26] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2[data-v-0db02a26] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3[data-v-0db02a26] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1[data-v-0db02a26]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2[data-v-0db02a26]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3[data-v-0db02a26] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3[data-v-0db02a26]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title[data-v-0db02a26] {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong[data-v-0db02a26] {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip[data-v-0db02a26] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.my[data-v-201003a4] {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.hdViewWechat[data-v-201003a4] {
    background: #7ebd8a!important;
    min-height: 50rem;
    width: 100%;
    height: 100%
}

.hdViewWechat .bkg[data-v-201003a4] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.hdViewWechat .time[data-v-201003a4] {
    padding-top: 12.5rem!important;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.hdViewWechat .event[data-v-201003a4] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.hdViewWechat .event .event-bkg-wrapper[data-v-201003a4] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.hdViewWechat .event .event-bkg-wrapper .event-bkg[data-v-201003a4] {
    width: 100%;
    height: auto
}

.hdViewWechat .event .event-desc[data-v-201003a4] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.hdViewWechat .event .event-desc .strong[data-v-201003a4] {
    color: #d3443e;
    font-weight: 700
}

.hdViewWechat .event .event-yun[data-v-201003a4] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.hdViewWechat .event .bank-1[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.hdViewWechat .event .bank-1[data-v-201003a4]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.hdViewWechat .event .bank-2[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.hdViewWechat .event .bank-chance[data-v-201003a4] {
    margin-top: .625rem
}

.hdViewWechat .event .bank-event[data-v-201003a4] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.hdViewWechat .event .bank-event img[data-v-201003a4] {
    display: inline-block;
    width: 48%
}

.hdViewWechat .event .bank-event img.disabled[data-v-201003a4] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.hdViewWechat .event .bank-event img[data-v-201003a4]:first-child {
    padding-right: 2%
}

.hdViewWechat .event .bank-3[data-v-201003a4] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.hdViewWechat .event .bank-3 span[data-v-201003a4] {
    color: #d3443e;
    font-weight: 700
}

.hdViewWechat .event .event-wx[data-v-201003a4] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.hdViewWechat .event .bkg-2[data-v-201003a4],.hdViewWechat .event .bkg-3[data-v-201003a4] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.hdViewWechat .rule[data-v-201003a4] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.hdViewWechat .rule img[data-v-201003a4] {
    width: 100%
}

.hdViewWechat .rule .bkg-1[data-v-201003a4] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.hdViewWechat .rule .bkg-2[data-v-201003a4] {
    position: absolute;
    z-index: 2;
    left: 0
}

.hdViewWechat .rule .bkg-3[data-v-201003a4] {
    position: absolute;
    z-index: 3;
    left: 0
}

.hdViewWechat .rule .rule-1[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem!important;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-1[data-v-201003a4]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-2[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-2[data-v-201003a4]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-3[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-3[data-v-201003a4]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-4[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-4[data-v-201003a4]:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-5[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-5[data-v-201003a4]:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-6[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-6[data-v-201003a4]:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-7[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-7[data-v-201003a4]:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-8[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-8[data-v-201003a4]:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .rule-9[data-v-201003a4] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.hdViewWechat .rule .rule-9[data-v-201003a4]:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.hdViewWechat .rule .title[data-v-201003a4] {
    color: #35714e;
    font-weight: 700
}

.hdViewWechat .rule .strong[data-v-201003a4] {
    color: #d3443e;
    font-weight: 700
}

.hdViewWechat .rule .tip[data-v-201003a4] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.channel-cel-new[data-v-40f4a73a] {
    margin-top: 12.5rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel[data-v-40f4a73a] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    padding: 1.25rem .625rem;
    text-align: left;
    color: #2c3e50;
    background: #fffbeb;
    border-radius: .625rem;
    font-size: 1rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-weight: 400
}

.channel-cel-new .bank-chance-cel .cel-title[data-v-40f4a73a] {
    font-size: 1.125rem
}

.channel-cel-new .bank-chance-cel .strong[data-v-40f4a73a] {
    color: #d3443e;
    font-weight: 700
}

.channel-cel-new .unionpay-cel[data-v-40f4a73a] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel .bank-event-flex[data-v-40f4a73a] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img[data-v-40f4a73a] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p[data-v-40f4a73a] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel[data-v-40f4a73a] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex[data-v-40f4a73a] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img[data-v-40f4a73a] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel[data-v-40f4a73a] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p[data-v-40f4a73a] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span[data-v-40f4a73a] {
    color: #d3443e
}

.home[data-v-40f4a73a] {
    background: #35714e url(../img/bkg-hd.512c1319.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.home .bank-event-flex[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .bank-event-flex img[data-v-40f4a73a] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home .time[data-v-40f4a73a] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event[data-v-40f4a73a] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home .event .event-bkg-wrapper[data-v-40f4a73a] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg[data-v-40f4a73a] {
    width: 100%;
    height: auto
}

.home .event .event-desc[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home .event .bank-1[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1[data-v-40f4a73a]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance[data-v-40f4a73a] {
    margin-top: .625rem
}

.home .event .bank-event[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img[data-v-40f4a73a] {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled[data-v-40f4a73a] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img[data-v-40f4a73a]:first-child {
    padding-right: 2%
}

.home .event .bank-3[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span[data-v-40f4a73a] {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2[data-v-40f4a73a],.home .event .bkg-3[data-v-40f4a73a] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule[data-v-40f4a73a] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img[data-v-40f4a73a] {
    width: 100%
}

.home .rule .bkg-1[data-v-40f4a73a] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2[data-v-40f4a73a] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3[data-v-40f4a73a] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1[data-v-40f4a73a]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2[data-v-40f4a73a]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3[data-v-40f4a73a] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3[data-v-40f4a73a]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title[data-v-40f4a73a] {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong[data-v-40f4a73a] {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip[data-v-40f4a73a] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.homeUni .rule .rule-none {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.channel-cel-new[data-v-5b0fdfa2] {
    margin-top: 15.625rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel[data-v-5b0fdfa2] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    padding: 2.5rem 0;
    text-align: center;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel[data-v-5b0fdfa2] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel .bank-event-flex[data-v-5b0fdfa2] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img[data-v-5b0fdfa2] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p[data-v-5b0fdfa2] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel[data-v-5b0fdfa2] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex[data-v-5b0fdfa2] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img[data-v-5b0fdfa2] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel[data-v-5b0fdfa2] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p[data-v-5b0fdfa2] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span[data-v-5b0fdfa2] {
    color: #d3443e
}

.home[data-v-5b0fdfa2] {
    background: #35714e url(../img/bkg3.7154d0e6.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.home .bank-event-flex[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .bank-event-flex img[data-v-5b0fdfa2] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home .time[data-v-5b0fdfa2] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event[data-v-5b0fdfa2] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home .event .event-bkg-wrapper[data-v-5b0fdfa2] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg[data-v-5b0fdfa2] {
    width: 100%;
    height: auto
}

.home .event .event-desc[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home .event .bank-1[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1[data-v-5b0fdfa2]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance[data-v-5b0fdfa2] {
    margin-top: .625rem
}

.home .event .bank-event[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img[data-v-5b0fdfa2] {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled[data-v-5b0fdfa2] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img[data-v-5b0fdfa2]:first-child {
    padding-right: 2%
}

.home .event .bank-3[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span[data-v-5b0fdfa2] {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2[data-v-5b0fdfa2],.home .event .bkg-3[data-v-5b0fdfa2] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule[data-v-5b0fdfa2] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img[data-v-5b0fdfa2] {
    width: 100%
}

.home .rule .bkg-1[data-v-5b0fdfa2] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2[data-v-5b0fdfa2] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3[data-v-5b0fdfa2] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1[data-v-5b0fdfa2]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2[data-v-5b0fdfa2]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3[data-v-5b0fdfa2] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3[data-v-5b0fdfa2]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title[data-v-5b0fdfa2] {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong[data-v-5b0fdfa2] {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip[data-v-5b0fdfa2] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.home .rule .rule-4 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-4:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-5 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-5:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-6 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-6:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-7 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-7:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-8 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-8:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-9 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-9:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home {
    background: #7ebd8a;
    min-height: 50rem
}

.home .time {
    padding-top: 30rem
}

.home {
    background: #7ebd8a url(../img/bkg2.2db65296.png) no-repeat;
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.home .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.home .time {
    padding-top: 14.375rem;
    margin: 0 auto
}

.home .event,.home .time {
    position: relative;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event {
    left: 5%;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem
}

.home .event .event-bkg-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg {
    width: 100%;
    height: auto
}

.home .event .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.home .event .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance {
    margin-top: .625rem
}

.home .event .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img:first-child {
    padding-right: 2%
}

.home .event .bank-event-flex {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event-flex img {
    width: 100%
}

.home .event .bank-event-flex img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event-flex img:first-child {
    padding-right: 2%
}

.home .event .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2,.home .event .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img {
    width: 100%
}

.home .rule .bkg-1 {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2 {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3 {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1 {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.home.bicycle {
    background: #6164c4 url(../img/bkg.324447d6.png);
    background-size: 100% auto;
    min-height: 50rem;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.home.bicycle .strong {
    color: #d3443e;
    font-weight: 700
}

.home.bicycle .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.home.bicycle .time {
    padding-top: 30rem;
    margin: 0 auto
}

.home.bicycle .event,.home.bicycle .time {
    position: relative;
    width: 90%;
    height: auto;
    z-index: 2
}

.home.bicycle .event {
    left: 5%;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem
}

.home.bicycle .event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home.bicycle .event .event-bkg-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home.bicycle .event .event-bkg-wrapper .event-bkg {
    width: 100%;
    height: auto
}

.home.bicycle .event .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home.bicycle .event .event-yun {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.home.bicycle .event .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home.bicycle .event .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home.bicycle .event .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home.bicycle .event .bank-chance {
    margin-top: .625rem
}

.home.bicycle .event .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.bicycle .event .bank-event img {
    display: inline-block;
    width: 48%
}

.home.bicycle .event .bank-event img:first-child {
    padding-right: 2%
}

.home.bicycle .event .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home.bicycle .event .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.home.bicycle .event .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home.bicycle .event .bkg-2,.home.bicycle .event .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home.bicycle .rule {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home.bicycle .rule img {
    width: 100%
}

.home.bicycle .rule .bkg-1 {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home.bicycle .rule .bkg-2 {
    position: absolute;
    z-index: 2;
    left: 0
}

.home.bicycle .rule .bkg-3 {
    position: absolute;
    z-index: 3;
    left: 0
}

.home.bicycle .rule .rule-1 {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycle .rule .rule-1:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycle .rule .rule-2 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycle .rule .rule-2:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycle .rule .rule-3 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycle .rule .rule-3:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycle .rule .title {
    color: #35714e;
    font-weight: 700
}

.home.bicycle .rule .tip {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.home.bicycleUni[data-v-954a59d2] {
    background: #6164c4 url(../img/bkg.324447d6.png);
    background-size: 100% auto;
    min-height: 50rem;
    background-repeat: no-repeat!important;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.home.bicycleUni .bank-event-flex[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.bicycleUni .bank-event-flex img[data-v-954a59d2] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home.bicycleUni .strong[data-v-954a59d2] {
    color: #d3443e;
    font-weight: 700
}

.home.bicycleUni .bkg[data-v-954a59d2] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.home.bicycleUni .time[data-v-954a59d2] {
    padding-top: 30rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home.bicycleUni .event[data-v-954a59d2] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home.bicycleUni .event img.disabled[data-v-954a59d2] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home.bicycleUni .event .event-bkg-wrapper[data-v-954a59d2] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home.bicycleUni .event .event-bkg-wrapper .event-bkg[data-v-954a59d2] {
    width: 100%;
    height: auto
}

.home.bicycleUni .event .event-desc[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home.bicycleUni .event .event-yun[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home.bicycleUni .event .bank-1[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home.bicycleUni .event .bank-1[data-v-954a59d2]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home.bicycleUni .event .bank-2[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem!important;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home.bicycleUni .event .bank-chance[data-v-954a59d2] {
    margin-top: .625rem
}

.home.bicycleUni .event .bank-event[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.bicycleUni .event .bank-event img[data-v-954a59d2] {
    display: inline-block;
    width: 48%
}

.home.bicycleUni .event .bank-event img[data-v-954a59d2]:first-child {
    padding-right: 2%
}

.home.bicycleUni .event .bank-3[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home.bicycleUni .event .bank-3 span[data-v-954a59d2] {
    color: #d3443e;
    font-weight: 700
}

.home.bicycleUni .event .event-wx[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home.bicycleUni .event .bkg-2[data-v-954a59d2],.home.bicycleUni .event .bkg-3[data-v-954a59d2] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home.bicycleUni .rule[data-v-954a59d2] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home.bicycleUni .rule img[data-v-954a59d2] {
    width: 100%
}

.home.bicycleUni .rule .bkg-1[data-v-954a59d2] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home.bicycleUni .rule .bkg-2[data-v-954a59d2] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home.bicycleUni .rule .bkg-3[data-v-954a59d2] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home.bicycleUni .rule .rule-1[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycleUni .rule .rule-1[data-v-954a59d2]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycleUni .rule .rule-2[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycleUni .rule .rule-2[data-v-954a59d2]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycleUni .rule .rule-3[data-v-954a59d2] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycleUni .rule .rule-3[data-v-954a59d2]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycleUni .rule .title[data-v-954a59d2] {
    color: #35714e;
    font-weight: 700
}

.home.bicycleUni .rule .tip[data-v-954a59d2] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.my[data-v-5c99aeb8] {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.homeplus.bicycle[data-v-5c99aeb8] {
    background: #6164c4 url(../img/bkg.324447d6.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    min-height: 50rem;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.homeplus.bicycle .bank-event-flex[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeplus.bicycle .bank-event-flex img[data-v-5c99aeb8] {
    width: 100%;
    margin-bottom: 1.25rem
}

.homeplus.bicycle .strong[data-v-5c99aeb8] {
    color: #d3443e;
    font-weight: 700
}

.homeplus.bicycle .bkg[data-v-5c99aeb8] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.homeplus.bicycle .time[data-v-5c99aeb8] {
    padding-top: 30rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.homeplus.bicycle .event[data-v-5c99aeb8] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.homeplus.bicycle .event img.disabled[data-v-5c99aeb8] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.homeplus.bicycle .event .event-bkg-wrapper[data-v-5c99aeb8] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.homeplus.bicycle .event .event-bkg-wrapper .event-bkg[data-v-5c99aeb8] {
    width: 100%;
    height: auto
}

.homeplus.bicycle .event .event-desc[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.homeplus.bicycle .event .event-yun[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.homeplus.bicycle .event .bank-1[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.homeplus.bicycle .event .bank-1[data-v-5c99aeb8]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.homeplus.bicycle .event .bank-2[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem!important;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.homeplus.bicycle .event .bank-chance[data-v-5c99aeb8] {
    margin-top: .625rem
}

.homeplus.bicycle .event .bank-event[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeplus.bicycle .event .bank-event img[data-v-5c99aeb8] {
    display: inline-block;
    width: 48%
}

.homeplus.bicycle .event .bank-event img[data-v-5c99aeb8]:first-child {
    padding-right: 2%
}

.homeplus.bicycle .event .bank-3[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.homeplus.bicycle .event .bank-3 span[data-v-5c99aeb8] {
    color: #d3443e;
    font-weight: 700
}

.homeplus.bicycle .event .event-wx[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.homeplus.bicycle .event .bkg-2[data-v-5c99aeb8],.homeplus.bicycle .event .bkg-3[data-v-5c99aeb8] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.homeplus.bicycle .rule[data-v-5c99aeb8] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.homeplus.bicycle .rule img[data-v-5c99aeb8] {
    width: 100%
}

.homeplus.bicycle .rule .bkg-1[data-v-5c99aeb8] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.homeplus.bicycle .rule .bkg-2[data-v-5c99aeb8] {
    position: absolute;
    z-index: 2;
    left: 0
}

.homeplus.bicycle .rule .bkg-3[data-v-5c99aeb8] {
    position: absolute;
    z-index: 3;
    left: 0
}

.homeplus.bicycle .rule .rule-1[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-1[data-v-5c99aeb8]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-2[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-2[data-v-5c99aeb8]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-3[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-3[data-v-5c99aeb8]:before {
    content: "3";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-4[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-4[data-v-5c99aeb8]:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-5[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-5[data-v-5c99aeb8]:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-6[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-6[data-v-5c99aeb8]:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-7[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-7[data-v-5c99aeb8]:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-8[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-8[data-v-5c99aeb8]:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .rule-9[data-v-5c99aeb8] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeplus.bicycle .rule .rule-9[data-v-5c99aeb8]:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeplus.bicycle .rule .title[data-v-5c99aeb8] {
    color: #35714e;
    font-weight: 700
}

.homeplus.bicycle .rule .tip[data-v-5c99aeb8] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.channel-cel[data-v-106cbde9] {
    margin-top: 15.625rem;
    position: relative
}

.channel-cel .base-image[data-v-106cbde9] {
    width: 90%
}

.channel-cel .bank-chance-cel[data-v-106cbde9] {
    position: absolute;
    top: 7%;
    left: 0;
    width: 100%;
    text-align: center;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.channel-cel .unionpay-cel[data-v-106cbde9] {
    position: absolute;
    top: 20%;
    width: 100%;
    padding: 0 1.25rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel .unionpay-cel .bank-event-flex[data-v-106cbde9] {
    width: 70%!important;
    margin: 0 auto
}

.channel-cel .unionpay-cel .bank-event-flex img[data-v-106cbde9] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel .unionpay-cel p[data-v-106cbde9] {
    width: 80%;
    margin-left: 10%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel .wechat-cel[data-v-106cbde9] {
    position: absolute;
    top: 56%;
    width: 100%;
    padding: 0 1.25rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel .wechat-cel .bank-event-flex[data-v-106cbde9] {
    width: 70%!important;
    margin: 0 auto
}

.channel-cel .wechat-cel .bank-event-flex img[data-v-106cbde9] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel .tips-cel[data-v-106cbde9] {
    position: absolute;
    top: 78%;
    width: 100%;
    padding: 0 1.25rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel .tips-cel p[data-v-106cbde9] {
    width: 80%;
    margin-left: 10%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel .tips-cel p span[data-v-106cbde9] {
    color: #d3443e
}

.home.bicycle[data-v-106cbde9] {
    background: #6164c4 url(../img/bkg2.b7722e41.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.home.bicycle .bank-event-flex[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.bicycle .bank-event-flex img[data-v-106cbde9] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home.bicycle .strong[data-v-106cbde9] {
    color: #d3443e;
    font-weight: 700
}

.home.bicycle .bkg[data-v-106cbde9] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.home.bicycle .time[data-v-106cbde9] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home.bicycle .event[data-v-106cbde9] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home.bicycle .event img.disabled[data-v-106cbde9] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home.bicycle .event .event-bkg-wrapper[data-v-106cbde9] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home.bicycle .event .event-bkg-wrapper .event-bkg[data-v-106cbde9] {
    width: 100%;
    height: auto
}

.home.bicycle .event .event-desc[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home.bicycle .event .event-desc-tips[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 0 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home.bicycle .event .event-yun[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home.bicycle .event .bank-1[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home.bicycle .event .bank-1[data-v-106cbde9]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home.bicycle .event .bank-2[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home.bicycle .event .bank-chance[data-v-106cbde9] {
    margin-top: .625rem
}

.home.bicycle .event .bank-event[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.bicycle .event .bank-event img[data-v-106cbde9] {
    display: inline-block;
    width: 48%
}

.home.bicycle .event .bank-event img[data-v-106cbde9]:first-child {
    padding-right: 2%
}

.home.bicycle .event .bank-3[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home.bicycle .event .bank-3 span[data-v-106cbde9] {
    color: #d3443e;
    font-weight: 700
}

.home.bicycle .event .event-wx[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home.bicycle .event .bkg-2[data-v-106cbde9],.home.bicycle .event .bkg-3[data-v-106cbde9] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home.bicycle .rule[data-v-106cbde9] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home.bicycle .rule img[data-v-106cbde9] {
    width: 100%
}

.home.bicycle .rule .bkg-1[data-v-106cbde9] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home.bicycle .rule .bkg-2[data-v-106cbde9] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home.bicycle .rule .bkg-3[data-v-106cbde9] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home.bicycle .rule .rule-1[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycle .rule .rule-1[data-v-106cbde9]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycle .rule .rule-2[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycle .rule .rule-2[data-v-106cbde9]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycle .rule .rule-3[data-v-106cbde9] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.bicycle .rule .rule-3[data-v-106cbde9]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #6164c4;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.bicycle .rule .title[data-v-106cbde9] {
    color: #35714e;
    font-weight: 700
}

.home.bicycle .rule .tip[data-v-106cbde9] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

img[data-v-74a8eefd] {
    width: 100%
}

.home.hd .event .event-desc-bank {
    padding: 0 1.875rem
}

.home.hd .event .event-desc-bank,.home.hd .rule .rule-4 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    text-align: left
}

.home.hd .rule .rule-4 {
    padding: 1.25rem 1.25rem 1.25rem 2.5rem
}

.home.hd .rule .rule-4:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-5 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-5:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-6 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-6:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-7 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-7:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-8 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-8:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-9 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-9:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd {
    background: #7ebd8a url(../img/bkg.a0019ca9.png);
    min-height: 50rem
}

.home.hd .time {
    padding-top: 30rem
}

.channel-cel-new {
    margin-top: 15.625rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel {
    padding: 2.5rem 0;
    text-align: center;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.channel-cel-new .bank-chance-cel,.channel-cel-new .unionpay-cel {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel {
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .unionpay-cel .bank-event-flex {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span {
    color: #d3443e
}

.home.hd {
    background: #7ebd8a url(../img/bkg1.288c2076.png);
    background-repeat: no-repeat;
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.home.hd .strong {
    color: #d3443e;
    font-weight: 700
}

.home.hd .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.home.hd .time {
    padding-top: 14.375rem;
    margin: 0 auto
}

.home.hd .event,.home.hd .time {
    position: relative;
    width: 90%;
    height: auto;
    z-index: 2
}

.home.hd .event {
    left: 5%;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem
}

.home.hd .event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home.hd .event .event-bkg-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home.hd .event .event-bkg-wrapper .event-bkg {
    width: 100%;
    height: auto
}

.home.hd .event .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home.hd .event .bank-event-flex {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.hd .event .bank-event-flex img {
    width: 100%
}

.home.hd .event .bank-event-flex img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home.hd .event .bank-event-flex img:first-child {
    padding-right: 2%
}

.home.hd .event .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home.hd .event .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home.hd .event .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home.hd .event .bank-chance {
    margin-top: .625rem
}

.home.hd .event .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home.hd .event .bank-event img {
    display: inline-block;
    width: 48%
}

.home.hd .event .bank-event img:first-child {
    padding-right: 2%
}

.home.hd .event .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home.hd .event .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.home.hd .event .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home.hd .event .bkg-2,.home.hd .event .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home.hd .rule {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home.hd .rule img {
    width: 100%
}

.home.hd .rule .bkg-1 {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home.hd .rule .bkg-2 {
    position: absolute;
    z-index: 2;
    left: 0
}

.home.hd .rule .bkg-3 {
    position: absolute;
    z-index: 3;
    left: 0
}

.home.hd .rule .rule-1 {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-1:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-2 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-2:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .rule-3 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home.hd .rule .rule-3:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home.hd .rule .title {
    color: #35714e;
    font-weight: 700
}

.home.hd .rule .tip {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.my {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.homeUni .event .event-desc .strong {
    color: #d3443e;
    font-weight: 700
}

.homeUni .rule .rule-1 {
    padding: 0 1.25rem 0 2.5rem
}

.homeUni .rule .rule-4 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-4:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-5 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-5:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-6 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-6:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-7 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-7:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-8 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-8:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-9 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-9:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

a:visited {
    color: #2e6da4
}

.homeUni {
    background: #7ebd8a;
    min-height: 50rem;
    width: 100%;
    height: 100%
}

.homeUni .bkg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.homeUni .time {
    padding-top: 12.5rem!important;
    margin: 0 auto
}

.homeUni .event,.homeUni .time {
    position: relative;
    width: 90%;
    height: auto;
    z-index: 2
}

.homeUni .event {
    left: 5%;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem
}

.homeUni .event .event-bkg-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.homeUni .event .event-bkg-wrapper .event-bkg {
    width: 100%;
    height: auto
}

.homeUni .event .event-desc {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.homeUni .event .event-yun {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.homeUni .event .bank-1 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.homeUni .event .bank-1:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.homeUni .event .bank-2 {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.homeUni .event .bank-chance {
    margin-top: .625rem
}

.homeUni .event .bank-event {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeUni .event .bank-event img {
    display: inline-block;
    width: 48%
}

.homeUni .event .bank-event img.disabled {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.homeUni .event .bank-event img:first-child {
    padding-right: 2%
}

.homeUni .event .bank-3 {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.homeUni .event .bank-3 span {
    color: #d3443e;
    font-weight: 700
}

.homeUni .event .event-wx {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.homeUni .event .bkg-2,.homeUni .event .bkg-3 {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.homeUni .rule {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.homeUni .rule img {
    width: 100%
}

.homeUni .rule .bkg-1 {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.homeUni .rule .bkg-2 {
    position: absolute;
    z-index: 2;
    left: 0
}

.homeUni .rule .bkg-3 {
    position: absolute;
    z-index: 3;
    left: 0
}

.homeUni .rule .rule-1 {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-1:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-2 {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-2:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .rule-3 {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeUni .rule .rule-3:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeUni .rule .title {
    color: #35714e;
    font-weight: 700
}

.homeUni .rule .strong {
    color: #d3443e;
    font-weight: 700
}

.homeUni .rule .tip,.homeUni .tip {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.channel-cel-new[data-v-5e6b338e] {
    margin-top: 12.5rem;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#81e48f),to(#37bd76));
    background: linear-gradient(180deg,#81e48f,#37bd76);
    width: 90%;
    padding: 1.25rem 0;
    border-radius: .625rem
}

.channel-cel-new .bank-chance-cel[data-v-5e6b338e] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    padding: 1.25rem;
    text-align: left;
    font-weight: 400;
    font-size: 1rem;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #2c3e50
}

.channel-cel-new .bank-chance-cel .strong[data-v-5e6b338e] {
    color: #d3443e;
    font-weight: 700
}

.channel-cel-new .unionpay-cel[data-v-5e6b338e] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .unionpay-cel .bank-event-flex[data-v-5e6b338e] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .unionpay-cel .bank-event-flex img[data-v-5e6b338e] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .unionpay-cel p[data-v-5e6b338e] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    color: #d3443e;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .wechat-cel[data-v-5e6b338e] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #fffbeb;
    border-radius: .625rem
}

.channel-cel-new .wechat-cel .bank-event-flex[data-v-5e6b338e] {
    width: 80%;
    margin: 0 auto
}

.channel-cel-new .wechat-cel .bank-event-flex img[data-v-5e6b338e] {
    width: 100%;
    margin-bottom: 0!important
}

.channel-cel-new .tips-cel[data-v-5e6b338e] {
    width: 96%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1.25rem;
    padding: 1.25rem 0;
    background: #fffbeb;
    border-radius: .625rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.channel-cel-new .tips-cel p[data-v-5e6b338e] {
    width: 90%;
    margin-left: 5%;
    text-align: left;
    font-weight: 700;
    margin-bottom: .625rem
}

.channel-cel-new .tips-cel p span[data-v-5e6b338e] {
    color: #d3443e
}

.home[data-v-5e6b338e] {
    background: #35714e url(../img/BJ_3C.********.png);
    background-size: 100% auto;
    min-height: 75rem;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.home .bank-event-flex[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .bank-event-flex img[data-v-5e6b338e] {
    width: 100%;
    margin-bottom: 1.25rem
}

.home .time[data-v-5e6b338e] {
    padding-top: 15.625rem;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.home .event[data-v-5e6b338e] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.home .event .event-bkg-wrapper[data-v-5e6b338e] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.home .event .event-bkg-wrapper .event-bkg[data-v-5e6b338e] {
    width: 100%;
    height: auto
}

.home .event .event-desc[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.home .event .event-yun[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    width: 20rem;
    height: 5.3125rem
}

.home .event .bank-1[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.home .event .bank-1[data-v-5e6b338e]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.home .event .bank-2[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.home .event .bank-chance[data-v-5e6b338e] {
    margin-top: .625rem
}

.home .event .bank-event[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.home .event .bank-event img[data-v-5e6b338e] {
    display: inline-block;
    width: 48%
}

.home .event .bank-event img.disabled[data-v-5e6b338e] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.home .event .bank-event img[data-v-5e6b338e]:first-child {
    padding-right: 2%
}

.home .event .bank-3[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.home .event .bank-3 span[data-v-5e6b338e] {
    color: #d3443e;
    font-weight: 700
}

.home .event .event-wx[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.home .event .bkg-2[data-v-5e6b338e],.home .event .bkg-3[data-v-5e6b338e] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule[data-v-5e6b338e] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.home .rule img[data-v-5e6b338e] {
    width: 100%
}

.home .rule .bkg-1[data-v-5e6b338e] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.home .rule .bkg-2[data-v-5e6b338e] {
    position: absolute;
    z-index: 2;
    left: 0
}

.home .rule .bkg-3[data-v-5e6b338e] {
    position: absolute;
    z-index: 3;
    left: 0
}

.home .rule .rule-1[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-1[data-v-5e6b338e]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-2[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-2[data-v-5e6b338e]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .rule-3[data-v-5e6b338e] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.home .rule .rule-3[data-v-5e6b338e]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.home .rule .title[data-v-5e6b338e] {
    color: #35714e;
    font-weight: 700
}

.home .rule .strong[data-v-5e6b338e] {
    color: #d3443e;
    font-weight: 700
}

.home .rule .tip[data-v-5e6b338e] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

#app[data-v-4bf21f82],body[data-v-4bf21f82],html[data-v-4bf21f82] {
    height: 100%
}

.coupon-none[data-v-4bf21f82] {
    margin: .9375rem .8125rem;
    background: #fff;
    -webkit-box-shadow: 0 .0625rem .0625rem 0 rgba(0,0,0,.06);
    box-shadow: 0 .0625rem .0625rem 0 rgba(0,0,0,.06);
    border-radius: .5625rem;
    padding: .8125rem .8125rem .8125rem .8125rem;
    position: relative;
    height: 3.375rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.my-list[data-v-4bf21f82] {
    background: #f6f6f6;
    min-height: 100%;
    width: 100%;
    padding-bottom: .625rem
}

.my-list .tabs[data-v-4bf21f82] {
    background: #fff;
    padding: 1.375rem 1.375rem .75rem 1.375rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: left;
    -ms-flex-pack: left;
    justify-content: left
}

.my-list .tabs .tab[data-v-4bf21f82] {
    padding: .3125rem .75rem;
    border-radius: 1.25rem;
    background: #f5f5f5;
    font-size: .875rem;
    margin-left: .6875rem;
    color: #666
}

.my-list .tabs .tab.active[data-v-4bf21f82] {
    color: #fff;
    background: #20b56d
}

.my-list .tips[data-v-4bf21f82] {
    font-size: .875rem;
    margin-top: .625rem;
    text-align: center;
    color: #d3443e;
    font-weight: 700
}

.my-list .item[data-v-4bf21f82] {
    margin: .9375rem .8125rem;
    background: #fff;
    -webkit-box-shadow: 0 .0625rem .0625rem 0 rgba(0,0,0,.06);
    box-shadow: 0 .0625rem .0625rem 0 rgba(0,0,0,.06);
    border-radius: .5625rem;
    padding: .8125rem .8125rem .8125rem 6.5625rem;
    position: relative;
    min-height: 5.25rem
}

.my-list .item.to-use[data-v-4bf21f82] {
    background: #fff url(../img/to-use-green.d4cd71b4.png);
    background-size: 3.375rem 3.375rem;
    background-repeat: no-repeat;
    background-position: right .8125rem center
}

.my-list .item.used[data-v-4bf21f82] {
    background: #fff url(../img/used.f70f7c54.png);
    background-size: 3.375rem 3.375rem;
    background-repeat: no-repeat;
    background-position: right .8125rem center
}

.my-list .item.used .left[data-v-4bf21f82],.my-list .item.used .right[data-v-4bf21f82] {
    opacity: .5
}

.my-list .item.expired[data-v-4bf21f82] {
    background: #fff url(../img/expired.d0889698.png);
    background-size: 3.375rem 3.375rem;
    background-repeat: no-repeat;
    background-position: right .8125rem center
}

.my-list .item.expired .left[data-v-4bf21f82],.my-list .item.expired .right[data-v-4bf21f82] {
    opacity: .5
}

.my-list .item .left[data-v-4bf21f82] {
    position: absolute;
    top: .8125rem;
    left: .8125rem;
    background: #eafbf1;
    border-radius: .1875rem;
    font-size: 1.0625rem;
    color: #20b56d;
    font-weight: 700;
    text-align: center;
    height: 5.25rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 5.25rem
}

.my-list .item .right[data-v-4bf21f82] {
    text-align: left
}

.my-list .item .right .title[data-v-4bf21f82] {
    font-size: .875rem;
    color: rgba(0,0,0,.8);
    font-weight: 700
}

.my-list .item .right .time[data-v-4bf21f82] {
    font-size: .75rem;
    color: hsla(0,0%,40%,.8);
    padding-top: .3125rem
}

.my-list .item .right .desc[data-v-4bf21f82] {
    position: absolute;
    bottom: .8125rem;
    left: 6.5625rem;
    font-size: .75rem;
    color: rgba(0,0,0,.8)
}

.my[data-v-4e7b7b67] {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.homeViewWechatOnline[data-v-4e7b7b67] {
    background: rgba(51,132,246,.65);
    min-height: 75rem!important;
    width: 100%;
    height: 100%
}

.homeViewWechatOnline .bkg[data-v-4e7b7b67] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.homeViewWechatOnline .time[data-v-4e7b7b67] {
    padding-top: 12.5rem!important;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.homeViewWechatOnline .event[data-v-4e7b7b67] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.homeViewWechatOnline .event .event-bkg-wrapper[data-v-4e7b7b67] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.homeViewWechatOnline .event .event-bkg-wrapper .event-bkg[data-v-4e7b7b67] {
    width: 100%;
    height: auto
}

.homeViewWechatOnline .event .event-desc[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.homeViewWechatOnline .event .event-desc .strong[data-v-4e7b7b67] {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechatOnline .event .event-yun[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.homeViewWechatOnline .event .bank-1[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.homeViewWechatOnline .event .bank-1[data-v-4e7b7b67]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.homeViewWechatOnline .event .bank-2[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.homeViewWechatOnline .event .bank-chance[data-v-4e7b7b67] {
    margin-top: .625rem
}

.homeViewWechatOnline .event .bank-event[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeViewWechatOnline .event .bank-event img[data-v-4e7b7b67] {
    display: inline-block;
    width: 48%
}

.homeViewWechatOnline .event .bank-event img.disabled[data-v-4e7b7b67] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.homeViewWechatOnline .event .bank-event img[data-v-4e7b7b67]:first-child {
    padding-right: 2%
}

.homeViewWechatOnline .event .bank-3[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.homeViewWechatOnline .event .bank-3 span[data-v-4e7b7b67] {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechatOnline .event .event-wx[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.homeViewWechatOnline .event .bkg-2[data-v-4e7b7b67],.homeViewWechatOnline .event .bkg-3[data-v-4e7b7b67] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.homeViewWechatOnline .rule[data-v-4e7b7b67] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.homeViewWechatOnline .rule img[data-v-4e7b7b67] {
    width: 100%
}

.homeViewWechatOnline .rule .bkg-1[data-v-4e7b7b67] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.homeViewWechatOnline .rule .bkg-2[data-v-4e7b7b67] {
    position: absolute;
    z-index: 2;
    left: 0
}

.homeViewWechatOnline .rule .bkg-3[data-v-4e7b7b67] {
    position: absolute;
    z-index: 3;
    left: 0
}

.homeViewWechatOnline .rule .rule-1[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-1[data-v-4e7b7b67]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-2[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-2[data-v-4e7b7b67]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-3[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-3[data-v-4e7b7b67]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-4[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-4[data-v-4e7b7b67]:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-5[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-5[data-v-4e7b7b67]:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-6[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-6[data-v-4e7b7b67]:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-7[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-7[data-v-4e7b7b67]:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-8[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-8[data-v-4e7b7b67]:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .rule-9[data-v-4e7b7b67] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline .rule .rule-9[data-v-4e7b7b67]:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline .rule .title[data-v-4e7b7b67] {
    color: #35714e;
    font-weight: 700
}

.homeViewWechatOnline .rule .strong[data-v-4e7b7b67] {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechatOnline .rule .tip[data-v-4e7b7b67] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

.my[data-v-c112765e] {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.homeViewWechatOnline2[data-v-c112765e] {
    background: #7ebd8a;
    min-height: 75rem!important;
    width: 100%;
    height: 100%
}

.homeViewWechatOnline2 .bkg[data-v-c112765e] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.homeViewWechatOnline2 .time[data-v-c112765e] {
    padding-top: 12.5rem!important;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.homeViewWechatOnline2 .event[data-v-c112765e] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.homeViewWechatOnline2 .event .event-bkg-wrapper[data-v-c112765e] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.homeViewWechatOnline2 .event .event-bkg-wrapper .event-bkg[data-v-c112765e] {
    width: 100%;
    height: auto
}

.homeViewWechatOnline2 .event .event-desc[data-v-c112765e] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.homeViewWechatOnline2 .event .event-desc .strong[data-v-c112765e] {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechatOnline2 .event .event-yun[data-v-c112765e] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.homeViewWechatOnline2 .event .bank-1[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.homeViewWechatOnline2 .event .bank-1[data-v-c112765e]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.homeViewWechatOnline2 .event .bank-2[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.homeViewWechatOnline2 .event .bank-chance[data-v-c112765e] {
    margin-top: .625rem
}

.homeViewWechatOnline2 .event .bank-event[data-v-c112765e] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.homeViewWechatOnline2 .event .bank-event img[data-v-c112765e] {
    display: inline-block;
    width: 48%
}

.homeViewWechatOnline2 .event .bank-event img.disabled[data-v-c112765e] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.homeViewWechatOnline2 .event .bank-event img[data-v-c112765e]:first-child {
    padding-right: 2%
}

.homeViewWechatOnline2 .event .bank-3[data-v-c112765e] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.homeViewWechatOnline2 .event .bank-3 span[data-v-c112765e] {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechatOnline2 .event .event-wx[data-v-c112765e] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.homeViewWechatOnline2 .event .bkg-2[data-v-c112765e],.homeViewWechatOnline2 .event .bkg-3[data-v-c112765e] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.homeViewWechatOnline2 .rule[data-v-c112765e] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.homeViewWechatOnline2 .rule img[data-v-c112765e] {
    width: 100%
}

.homeViewWechatOnline2 .rule .bkg-1[data-v-c112765e] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.homeViewWechatOnline2 .rule .bkg-2[data-v-c112765e] {
    position: absolute;
    z-index: 2;
    left: 0
}

.homeViewWechatOnline2 .rule .bkg-3[data-v-c112765e] {
    position: absolute;
    z-index: 3;
    left: 0
}

.homeViewWechatOnline2 .rule .rule-1[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 5rem 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-1[data-v-c112765e]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-2[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-2[data-v-c112765e]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-3[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-3[data-v-c112765e]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-4[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-4[data-v-c112765e]:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-5[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-5[data-v-c112765e]:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-6[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-6[data-v-c112765e]:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-7[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-7[data-v-c112765e]:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-8[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-8[data-v-c112765e]:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .rule-9[data-v-c112765e] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.homeViewWechatOnline2 .rule .rule-9[data-v-c112765e]:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.homeViewWechatOnline2 .rule .title[data-v-c112765e] {
    color: #35714e;
    font-weight: 700
}

.homeViewWechatOnline2 .rule .strong[data-v-c112765e] {
    color: #d3443e;
    font-weight: 700
}

.homeViewWechatOnline2 .rule .tip[data-v-c112765e] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

a[data-v-********]:visited {
    color: #2e6da4
}

.my[data-v-********] {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.WechatOnline3C[data-v-********] {
    background: #7ebd8a;
    min-height: 75rem!important;
    width: 100%;
    height: 100%
}

.WechatOnline3C .bkg[data-v-********] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.WechatOnline3C .time[data-v-********] {
    padding-top: 12.5rem!important;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.WechatOnline3C .event[data-v-********] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.WechatOnline3C .event .event-bkg-wrapper[data-v-********] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.WechatOnline3C .event .event-bkg-wrapper .event-bkg[data-v-********] {
    width: 100%;
    height: auto
}

.WechatOnline3C .event .event-desc[data-v-********] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.WechatOnline3C .event .event-desc .strong[data-v-********] {
    color: #d3443e;
    font-weight: 700
}

.WechatOnline3C .event .event-yun[data-v-********] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.WechatOnline3C .event .bank-1[data-v-********] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.WechatOnline3C .event .bank-1[data-v-********]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.WechatOnline3C .event .bank-2[data-v-********] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.WechatOnline3C .event .bank-chance[data-v-********] {
    margin-top: .625rem
}

.WechatOnline3C .event .bank-event[data-v-********] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.WechatOnline3C .event .bank-event img[data-v-********] {
    display: inline-block;
    width: 48%
}

.WechatOnline3C .event .bank-event img.disabled[data-v-********] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.WechatOnline3C .event .bank-event img[data-v-********]:first-child {
    padding-right: 2%
}

.WechatOnline3C .event .bank-3[data-v-********] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.WechatOnline3C .event .bank-3 span[data-v-********] {
    color: #d3443e;
    font-weight: 700
}

.WechatOnline3C .event .event-wx[data-v-********] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.WechatOnline3C .event .bkg-2[data-v-********],.WechatOnline3C .event .bkg-3[data-v-********] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.WechatOnline3C .rule[data-v-********] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.WechatOnline3C .rule img[data-v-********] {
    width: 100%
}

.WechatOnline3C .rule .bkg-1[data-v-********] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.WechatOnline3C .rule .bkg-2[data-v-********] {
    position: absolute;
    z-index: 2;
    left: 0
}

.WechatOnline3C .rule .bkg-3[data-v-********] {
    position: absolute;
    z-index: 3;
    left: 0
}

.WechatOnline3C .rule .rule-1[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-1[data-v-********]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-2[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-2[data-v-********]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-3[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-3[data-v-********]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-4[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-4[data-v-********]:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-5[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-5[data-v-********]:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-6[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-6[data-v-********]:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-7[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-7[data-v-********]:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-8[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-8[data-v-********]:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .rule-9[data-v-********] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C .rule .rule-9[data-v-********]:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C .rule .title[data-v-********] {
    color: #35714e;
    font-weight: 700
}

.WechatOnline3C .rule .strong[data-v-********] {
    color: #d3443e;
    font-weight: 700
}

.WechatOnline3C .rule .tip[data-v-********] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}

a[data-v-1589bd08]:visited {
    color: #2e6da4
}

.my[data-v-1589bd08] {
    position: fixed;
    right: 0;
    top: 20%;
    width: 3.75rem;
    z-index: 33
}

.WechatOnline3C2[data-v-1589bd08] {
    background: #7ebd8a;
    min-height: 75rem!important;
    width: 100%;
    height: 100%
}

.WechatOnline3C2 .bkg[data-v-1589bd08] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 1
}

.WechatOnline3C2 .time[data-v-1589bd08] {
    padding-top: 12.5rem!important;
    position: relative;
    margin: 0 auto;
    width: 90%;
    height: auto;
    z-index: 2
}

.WechatOnline3C2 .event[data-v-1589bd08] {
    position: relative;
    left: 5%;
    width: 90%;
    height: auto;
    margin-bottom: 1.25rem;
    padding-bottom: .625rem;
    z-index: 2
}

.WechatOnline3C2 .event .event-bkg-wrapper[data-v-1589bd08] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-bottom-left-radius: .9375rem;
    border-bottom-right-radius: .9375rem;
    overflow: hidden
}

.WechatOnline3C2 .event .event-bkg-wrapper .event-bkg[data-v-1589bd08] {
    width: 100%;
    height: auto
}

.WechatOnline3C2 .event .event-desc[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 6.25rem 1.875rem 0 1.875rem;
    text-align: left
}

.WechatOnline3C2 .event .event-desc .strong[data-v-1589bd08] {
    color: #d3443e;
    font-weight: 700
}

.WechatOnline3C2 .event .event-yun[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem
}

.WechatOnline3C2 .event .bank-1[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 0 1.875rem;
    color: #d67d50;
    font-weight: 700;
    font-size: 1.6875rem
}

.WechatOnline3C2 .event .bank-1[data-v-1589bd08]:before {
    content: "";
    position: absolute;
    bottom: -.4375rem;
    left: 50%;
    margin-left: -4.6875rem;
    width: 9.375rem;
    height: .3125rem;
    z-index: 4;
    background: #f5e3ca;
    border-radius: .1875rem
}

.WechatOnline3C2 .event .bank-2[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: .625rem 1.875rem 1.25rem 1.875rem;
    color: #d3443e;
    font-weight: 700;
    font-size: 1.375rem
}

.WechatOnline3C2 .event .bank-chance[data-v-1589bd08] {
    margin-top: .625rem
}

.WechatOnline3C2 .event .bank-event[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    width: 90%;
    margin: 0 auto
}

.WechatOnline3C2 .event .bank-event img[data-v-1589bd08] {
    display: inline-block;
    width: 48%
}

.WechatOnline3C2 .event .bank-event img.disabled[data-v-1589bd08] {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.WechatOnline3C2 .event .bank-event img[data-v-1589bd08]:first-child {
    padding-right: 2%
}

.WechatOnline3C2 .event .bank-3[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    font-size: 1.125rem;
    padding: 1.25rem 1.875rem 1.25rem 1.875rem;
    text-align: left
}

.WechatOnline3C2 .event .bank-3 span[data-v-1589bd08] {
    color: #d3443e;
    font-weight: 700
}

.WechatOnline3C2 .event .event-wx[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    width: 18.75rem;
    height: 5rem;
    padding-bottom: 1.25rem
}

.WechatOnline3C2 .event .bkg-2[data-v-1589bd08],.WechatOnline3C2 .event .bkg-3[data-v-1589bd08] {
    width: 100%;
    position: absolute;
    z-index: 2;
    left: 0
}

.WechatOnline3C2 .rule[data-v-1589bd08] {
    position: relative;
    left: 5%;
    width: 90%;
    z-index: 2
}

.WechatOnline3C2 .rule img[data-v-1589bd08] {
    width: 100%
}

.WechatOnline3C2 .rule .bkg-1[data-v-1589bd08] {
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0
}

.WechatOnline3C2 .rule .bkg-2[data-v-1589bd08] {
    position: absolute;
    z-index: 2;
    left: 0
}

.WechatOnline3C2 .rule .bkg-3[data-v-1589bd08] {
    position: absolute;
    z-index: 3;
    left: 0
}

.WechatOnline3C2 .rule .rule-1[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-1[data-v-1589bd08]:before {
    content: "1";
    position: absolute;
    top: 5.3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-2[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-2[data-v-1589bd08]:before {
    content: "2";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-3[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-3[data-v-1589bd08]:before {
    content: "3";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-4[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-4[data-v-1589bd08]:before {
    content: "4";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-5[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-5[data-v-1589bd08]:before {
    content: "5";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-6[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-6[data-v-1589bd08]:before {
    content: "6";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-7[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-7[data-v-1589bd08]:before {
    content: "7";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-8[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 1.25rem 1.25rem 1.25rem 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-8[data-v-1589bd08]:before {
    content: "8";
    position: absolute;
    top: 1.625rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .rule-9[data-v-1589bd08] {
    position: relative;
    z-index: 3;
    padding: 0 1.25rem 0 2.5rem;
    text-align: left;
    font-size: 1.125rem
}

.WechatOnline3C2 .rule .rule-9[data-v-1589bd08]:before {
    content: "9";
    position: absolute;
    top: .3125rem;
    left: 1.25rem;
    color: #fff;
    background: #35714e;
    width: .9375rem;
    height: .9375rem;
    border-radius: .9375rem;
    text-align: center;
    line-height: .9375rem;
    font-size: .75rem
}

.WechatOnline3C2 .rule .title[data-v-1589bd08] {
    color: #35714e;
    font-weight: 700
}

.WechatOnline3C2 .rule .strong[data-v-1589bd08] {
    color: #d3443e;
    font-weight: 700
}

.WechatOnline3C2 .rule .tip[data-v-1589bd08] {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
    position: relative;
    z-index: 3;
    color: #d3443e;
    font-size: .9375rem;
    text-align: left
}
